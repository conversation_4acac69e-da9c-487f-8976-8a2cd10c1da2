import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useThemeStore } from '../../stores/themeStore';
import { DynamicAppHeader } from '../layout/DynamicAppHeader';
import { cn } from '../../utils/cn';

// Mock app data - this would come from an API in a real app
const mockAppsData = {
  '1': {
    id: '1',
    name: 'Sales',
    icon: '💼',
    color: '#f97316', // orange-500
    navLinks: [
      { label: 'Dashboard', href: '/app?menu=1&view=dashboard', isActive: true },
      { label: 'Orders', href: '/app?menu=1&view=orders', isActive: false },
      { label: 'Customers', href: '/app?menu=1&view=customers', isActive: false },
      { label: 'Products', href: '/app?menu=1&view=products', isActive: false },
      { label: 'Reports', href: '/app?menu=1&view=reports', isActive: false },
    ],
    views: {
      dashboard: {
        title: 'Sales Dashboard',
        content: 'Welcome to the Sales Dashboard. Here you can view your sales metrics and performance.',
      },
      orders: {
        title: 'Sales Orders',
        content: 'Manage your sales orders and track their status.',
      },
      customers: {
        title: 'Customer Management',
        content: 'View and manage your customer database.',
      },
      products: {
        title: 'Product Catalog',
        content: 'Manage your product catalog and pricing.',
      },
      reports: {
        title: 'Sales Reports',
        content: 'Generate and view sales reports and analytics.',
      },
    },
  },
  '2': {
    id: '2',
    name: 'Inventory',
    icon: '📦',
    color: '#3b82f6', // blue-500
    navLinks: [
      { label: 'Overview', href: '/app?menu=2&view=overview', isActive: true },
      { label: 'Stock', href: '/app?menu=2&view=stock', isActive: false },
      { label: 'Warehouses', href: '/app?menu=2&view=warehouses', isActive: false },
      { label: 'Transfers', href: '/app?menu=2&view=transfers', isActive: false },
    ],
    views: {
      overview: {
        title: 'Inventory Overview',
        content: 'Get an overview of your inventory levels and stock movements.',
      },
      stock: {
        title: 'Stock Management',
        content: 'Manage your stock levels and inventory items.',
      },
      warehouses: {
        title: 'Warehouse Management',
        content: 'Manage your warehouse locations and configurations.',
      },
      transfers: {
        title: 'Stock Transfers',
        content: 'Track and manage stock transfers between locations.',
      },
    },
  },
  '10': {
    id: '10',
    name: 'Discuss',
    icon: '💬',
    color: '#8b5cf6', // violet-500
    navLinks: [
      { label: 'Channels', href: '/app?menu=10&view=channels', isActive: true },
      { label: 'Direct Messages', href: '/app?menu=10&view=messages', isActive: false },
      { label: 'Teams', href: '/app?menu=10&view=teams', isActive: false },
      { label: 'Settings', href: '/app?menu=10&view=settings', isActive: false },
    ],
    views: {
      channels: {
        title: 'Team Channels',
        content: 'Communicate with your team through organized channels.',
      },
      messages: {
        title: 'Direct Messages',
        content: 'Private conversations with team members.',
      },
      teams: {
        title: 'Team Management',
        content: 'Manage your teams and team members.',
      },
      settings: {
        title: 'Discussion Settings',
        content: 'Configure your discussion preferences and notifications.',
      },
    },
  },
};

// Mock user data
const mockUserData = {
  name: 'John Doe',
  avatar: '👤',
  notifications: [
    { count: 3, icon: '🔔' },
  ],
};

export interface DynamicAppViewProps {
  className?: string;
  'data-testid'?: string;
}

const DynamicAppView: React.FC<DynamicAppViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { colors, isDark } = useThemeStore();
  
  const menuId = searchParams.get('menu');
  const viewId = searchParams.get('view') || 'dashboard';
  
  // Get app data based on menu ID
  const appData = menuId ? mockAppsData[menuId as keyof typeof mockAppsData] : null;
  
  // Redirect to dashboard if no valid app is found
  useEffect(() => {
    if (!menuId || !appData) {
      navigate('/dashboard');
    }
  }, [menuId, appData, navigate]);
  
  if (!appData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2" style={{ color: colors.text }}>
            App not found
          </h2>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            Redirecting to dashboard...
          </p>
        </div>
      </div>
    );
  }
  
  // Update nav links to show active state based on current view
  const updatedNavLinks = appData.navLinks.map(link => ({
    ...link,
    isActive: link.href.includes(`view=${viewId}`),
  }));
  
  const currentView = appData.views[viewId as keyof typeof appData.views] || appData.views.dashboard;
  
  // Create view data for the header
  const viewData = {
    title: currentView.title,
    actions: [
      { label: 'New', onClick: () => console.log('New clicked'), isPrimary: true },
      { label: 'Import', onClick: () => console.log('Import clicked'), isPrimary: false },
    ],
    search: {
      filters: [
        { id: 'active', label: 'Active' },
        { id: 'archived', label: 'Archived' },
      ],
      onSearch: (query: string) => console.log('Search:', query),
      onRemoveFilter: (id: any) => console.log('Remove filter:', id),
    },
    pagination: {
      currentRange: '1-20 / 100',
      onNext: () => console.log('Next page'),
      onPrev: () => console.log('Previous page'),
    },
    viewModes: [
      { name: 'List', icon: '📋' },
      { name: 'Grid', icon: '⊞' },
      { name: 'Chart', icon: '📊' },
    ],
    activeViewMode: 'List',
  };
  
  const appHeaderData = {
    name: appData.name,
    icon: (
      <div
        className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-lg"
        style={{ backgroundColor: appData.color }}
      >
        {appData.icon}
      </div>
    ),
    navLinks: updatedNavLinks,
  };
  
  return (
    <div
      className={cn('min-h-screen', className)}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {/* Dynamic App Header */}
      <DynamicAppHeader
        app={appHeaderData}
        user={mockUserData}
        view={viewData}
      />
      
      {/* Main Content Area */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm">
            <button
              onClick={() => navigate('/dashboard')}
              className="hover:underline"
              style={{ color: colors.textSecondary }}
            >
              Dashboard
            </button>
            <span style={{ color: colors.textSecondary }}>/</span>
            <span style={{ color: colors.text }}>{appData.name}</span>
            <span style={{ color: colors.textSecondary }}>/</span>
            <span style={{ color: colors.text }}>{currentView.title}</span>
          </nav>
          
          {/* Content Area */}
          <div
            className="rounded-lg border p-6"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
            }}
          >
            <div className="text-center py-12">
              <div
                className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl"
                style={{ backgroundColor: appData.color }}
              >
                {appData.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
                {currentView.title}
              </h3>
              <p className="text-sm max-w-md mx-auto" style={{ color: colors.textSecondary }}>
                {currentView.content}
              </p>
              <div className="mt-6">
                <button
                  className="px-4 py-2 rounded-lg text-white font-medium"
                  style={{ backgroundColor: appData.color }}
                  onClick={() => console.log('Get started clicked')}
                >
                  Get Started
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default DynamicAppView;
