import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import RootLayout from '../pages/layout';
import HomePage from '../pages/index';
import DemoPage from '../pages/demo';
import DashboardPage from '../pages/dashboard';
import AppHeaderDemoPage from '../pages/app-header-demo';

// Define routes using React Router v6
const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'demo',
        element: <DemoPage />,
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      {
        path: 'app-header-demo',
        element: <AppHeaderDemoPage />,
      },
    ],
  },
]);

export function AppRouter() {
  return <RouterProvider router={router} />;
}
